user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
	worker_connections 768;
	# multi_accept on;
}

http {

	##
	# Basic Settings
	##

	sendfile on;
	tcp_nopush on;
	tcp_nodelay on;
	keepalive_timeout 65;
	types_hash_max_size 2048;
	# server_tokens off;

	# server_names_hash_bucket_size 64;
	# server_name_in_redirect off;

	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	##
	# SSL Settings
	##

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3; # Dropping SSLv3, ref: POODLE
	ssl_prefer_server_ciphers on;

	##
	# Logging Settings
	##

	access_log /var/log/nginx/access.log;
	error_log /var/log/nginx/error.log;

	##
	# Gzip Settings
	##

	gzip on;

	# gzip_vary on;
	# gzip_proxied any;
	# gzip_comp_level 6;
	# gzip_buffers 16 8k;
	# gzip_http_version 1.1;
	# gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

	##
	# Virtual Host Configs
	##

	include /etc/nginx/conf.d/*.conf;
	include /etc/nginx/sites-enabled/*;

	ssl_certificate vupbi.online_bundle.crt;
	ssl_certificate_key vupbi.online.key; 

	server {
		listen 443 ssl;
		server_name vupbi.online;
		return 301 https://$host$request_uri; 
		ssl_certificate vupbi.online_bundle.crt;
		ssl_certificate_key vupbi.online.key; 
		ssl_session_timeout 5m;
		ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
		#请按照以下协议配置
		ssl_protocols TLSv1.2 TLSv1.3;
		ssl_prefer_server_ciphers on;

		location / {
			root /root/vupbi/frontend/dist;
			try_files $uri $uri/ /index.html;
		}
	}
	server {
	listen 80;
	#请填写绑定证书的域名
	server_name vupbi.online;
	#把http的域名请求转成https
	return 301 https://$host$request_uri; 
	}
	server {
	listen 9022;
	#请填写绑定证书的域名
	server_name vupbi.online;
    location / {
        proxy_pass http://127.0.0.1:9022;  # 转发到 Uvicorn
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
	}
}


#mail {
#	# See sample authentication script at:
#	# http://wiki.nginx.org/ImapAuthenticateWithApachePhpScript
# 
#	# auth_http localhost/auth.php;
#	# pop3_capabilities "TOP" "USER";
#	# imap_capabilities "IMAP4rev1" "UIDPLUS";
# 
#	server {
#		listen     localhost:110;
#		protocol   pop3;
#		proxy      on;
#	}
# 
#	server {
#		listen     localhost:143;
#		protocol   imap;
#		proxy      on;
#	}
#}

